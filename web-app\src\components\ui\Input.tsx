import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '../../lib/utils'
import { Eye, EyeOff } from 'lucide-react'

const inputVariants = cva(
  "flex w-full rounded-xl border bg-transparent px-4 py-3 text-sm transition-all duration-300 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-400 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "border-slate-600 bg-slate-700/50 text-white focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/50 focus:shadow-lg focus:shadow-yellow-400/10",
        premium: "border-purple-500/50 bg-slate-800/50 text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400/50 focus:shadow-lg focus:shadow-purple-400/10 backdrop-blur-sm",
        glass: "border-white/20 bg-white/10 text-white focus:border-white/40 focus:ring-2 focus:ring-white/20 focus:shadow-lg focus:shadow-white/10 backdrop-blur-md",
        luxury: "border-amber-400/50 bg-gradient-to-r from-amber-50/5 to-yellow-50/5 text-white focus:border-amber-400 focus:ring-2 focus:ring-amber-400/50 focus:shadow-lg focus:shadow-amber-400/10",
      },
      size: {
        default: "h-12 px-4 py-3",
        sm: "h-9 px-3 py-2 text-xs",
        lg: "h-14 px-5 py-4 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof inputVariants> {
  label?: string
  error?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  showPasswordToggle?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    variant, 
    size, 
    type, 
    label, 
    error, 
    leftIcon, 
    rightIcon, 
    showPasswordToggle,
    id,
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false)
    const [isFocused, setIsFocused] = useState(false)
    const [hasValue, setHasValue] = useState(false)
    
    const inputType = showPasswordToggle && type === 'password' 
      ? (showPassword ? 'text' : 'password') 
      : type

    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(e.target.value.length > 0)
      props.onChange?.(e)
    }

    return (
      <div className="space-y-2">
        {/* Label */}
        {label && (
          <motion.label
            htmlFor={inputId}
            className={cn(
              "block text-sm font-medium transition-colors duration-300",
              error ? "text-red-400" : "text-slate-300",
              isFocused && !error && "text-yellow-400"
            )}
            animate={{ 
              color: error ? "#f87171" : isFocused ? "#fbbf24" : "#cbd5e1" 
            }}
          >
            {label}
          </motion.label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div className={cn(
                "h-5 w-5 transition-colors duration-300",
                error ? "text-red-400" : isFocused ? "text-yellow-400" : "text-slate-400"
              )}>
                {leftIcon}
              </div>
            </div>
          )}

          {/* Input Field */}
          <input
            ref={ref}
            id={inputId}
            type={inputType}
            className={cn(
              inputVariants({ variant, size }),
              leftIcon && "pl-10",
              (rightIcon || showPasswordToggle) && "pr-10",
              error && "border-red-500 focus:border-red-400 focus:ring-red-400/50",
              className
            )}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onChange={handleInputChange}
            {...props}
          />

          {/* Right Icon or Password Toggle */}
          {(rightIcon || showPasswordToggle) && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {showPasswordToggle && type === 'password' ? (
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={cn(
                    "h-5 w-5 transition-colors duration-300 hover:scale-110",
                    error ? "text-red-400" : isFocused ? "text-yellow-400" : "text-slate-400"
                  )}
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              ) : rightIcon ? (
                <div className={cn(
                  "h-5 w-5 transition-colors duration-300",
                  error ? "text-red-400" : isFocused ? "text-yellow-400" : "text-slate-400"
                )}>
                  {rightIcon}
                </div>
              ) : null}
            </div>
          )}

          {/* Focus Ring Animation */}
          <AnimatePresence>
            {isFocused && (
              <motion.div
                className="absolute inset-0 rounded-xl border-2 border-yellow-400/50 pointer-events-none"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              />
            )}
          </AnimatePresence>

          {/* Floating Label Animation */}
          {label && (
            <AnimatePresence>
              {(isFocused || hasValue) && (
                <motion.div
                  className="absolute -top-2 left-3 px-1 bg-slate-900 text-xs font-medium text-yellow-400"
                  initial={{ opacity: 0, y: 10, scale: 0.8 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  {label}
                </motion.div>
              )}
            </AnimatePresence>
          )}
        </div>

        {/* Error Message */}
        <AnimatePresence>
          {error && (
            <motion.p
              className="text-sm text-red-400 flex items-center gap-1"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {error}
            </motion.p>
          )}
        </AnimatePresence>
      </div>
    )
  }
)

Input.displayName = "Input"

export { Input, inputVariants }

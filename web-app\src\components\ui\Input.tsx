import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '../../lib/utils'
import { Eye, EyeOff } from 'lucide-react'

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  variant?: 'default' | 'mystical' | 'divine'
  size?: 'sm' | 'md' | 'lg'
  floatingLabel?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    type,
    label,
    error,
    leftIcon,
    rightIcon,
    variant = 'default',
    size = 'md',
    floatingLabel = false,
    disabled,
    ...props
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false)
    const [showPassword, setShowPassword] = useState(false)
    const [hasValue, setHasValue] = useState(!!props.value || !!props.defaultValue)

    const isPasswordType = type === 'password'
    const inputType = isPasswordType && showPassword ? 'text' : type

    // Base styles
    const baseStyles = "w-full transition-all duration-300 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed bg-slate-800/50 backdrop-blur-sm border text-white placeholder:text-slate-400"

    // Variant styles
    const variantStyles = {
      default: cn(
        "border-slate-600 focus:border-amber-500 focus:ring-2 focus:ring-amber-500/20",
        error && "border-red-500 focus:border-red-500 focus:ring-red-500/20"
      ),
      mystical: cn(
        "border-purple-500/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 shadow-lg focus:shadow-purple-400/10",
        error && "border-red-500 focus:border-red-500 focus:ring-red-500/20"
      ),
      divine: cn(
        "border-amber-500/50 focus:border-amber-400 focus:ring-2 focus:ring-amber-400/20 shadow-lg focus:shadow-amber-400/10",
        error && "border-red-500 focus:border-red-500 focus:ring-red-500/20"
      )
    }

    // Size styles
    const sizeStyles = {
      sm: "h-9 px-3 py-2 text-sm rounded-lg",
      md: "h-11 px-4 py-2.5 text-sm rounded-xl",
      lg: "h-12 px-6 py-3 text-base rounded-xl"
    }

    const iconSizes = {
      sm: 16,
      md: 20,
      lg: 24
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(!!e.target.value)
      props.onChange?.(e)
    }

    return (
      <div className="space-y-2">
        {/* Label */}
        {label && !floatingLabel && (
          <label className="block text-sm font-medium text-slate-300">
            {label}
          </label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Floating Label */}
          {floatingLabel && label && (
            <motion.label
              className={cn(
                "absolute left-3 text-slate-400 pointer-events-none transition-all duration-200",
                (isFocused || hasValue)
                  ? "top-2 text-xs text-amber-400"
                  : "top-1/2 -translate-y-1/2 text-sm"
              )}
              animate={{
                y: (isFocused || hasValue) ? -8 : 0,
                scale: (isFocused || hasValue) ? 0.85 : 1,
                color: isFocused ? "#fbbf24" : "#94a3b8"
              }}
            >
              {label}
            </motion.label>
          )}

          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div className={cn(
                "transition-colors duration-300",
                error ? "text-red-400" : isFocused ? "text-amber-400" : "text-slate-400"
              )}>
                {React.cloneElement(leftIcon as React.ReactElement, {
                  size: iconSizes[size]
                })}
              </div>
            </div>
          )}

          {/* Input Field */}
          <motion.input
            ref={ref}
            type={inputType}
            className={cn(
              baseStyles,
              variantStyles[variant],
              sizeStyles[size],
              leftIcon && "pl-10",
              (rightIcon || isPasswordType) && "pr-10",
              floatingLabel && "pt-6 pb-2",
              className
            )}
            disabled={disabled}
            onFocus={(e) => {
              setIsFocused(true)
              props.onFocus?.(e)
            }}
            onBlur={(e) => {
              setIsFocused(false)
              props.onBlur?.(e)
            }}
            onChange={handleInputChange}
            whileFocus={{ scale: 1.01 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            {...props}
          />

          {/* Right Icon / Password Toggle */}
          {(rightIcon || isPasswordType) && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {isPasswordType ? (
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={cn(
                    "transition-colors duration-300 hover:scale-110",
                    error ? "text-red-400" : isFocused ? "text-amber-400" : "text-slate-400"
                  )}
                >
                  {showPassword ? <EyeOff size={iconSizes[size]} /> : <Eye size={iconSizes[size]} />}
                </button>
              ) : rightIcon ? (
                <div className={cn(
                  "transition-colors duration-300",
                  error ? "text-red-400" : isFocused ? "text-amber-400" : "text-slate-400"
                )}>
                  {React.cloneElement(rightIcon as React.ReactElement, {
                    size: iconSizes[size]
                  })}
                </div>
              ) : null}
            </div>
          )}

          {/* Focus Ring Effect */}
          <AnimatePresence>
            {isFocused && (
              <motion.div
                className="absolute inset-0 rounded-xl border-2 border-amber-400/50 pointer-events-none"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              />
            )}
          </AnimatePresence>
        </div>

        {/* Error Message */}
        <AnimatePresence>
          {error && (
            <motion.p
              className="text-sm text-red-400 flex items-center gap-1"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <span className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center text-xs text-white">!</span>
              {error}
            </motion.p>
          )}
        </AnimatePresence>
      </div>
    )
  }
)

Input.displayName = "Input"

export { Input }
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  background-attachment: fixed;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a2e;
}

::-webkit-scrollbar-thumb {
  background: #d4af37;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #f4d03f;
}

/* Mythology themed components */
.mythology-card {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

/* Ensure Tailwind animations work */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
    transform: translateX(-100%);
  }
  100% {
    background-position: 200% 0;
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200% 100%;
}

.mythology-card:hover {
  border-color: rgba(212, 175, 55, 0.4);
  box-shadow: 0 25px 50px -12px rgba(212, 175, 55, 0.1);
  transform: translateY(-2px);
}

.mythology-button {
  background: linear-gradient(90deg, #d4af37 0%, #cd7f32 100%);
  color: #0a0a0a;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1);
  transition: all 0.3s ease;
}

.mythology-button:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.mythology-text-glow {
  text-shadow: 0 0 10px #d4af37, 0 0 20px #d4af37, 0 0 30px #d4af37;
}

.kubera-gradient-text {
  background: linear-gradient(135deg, #d4af37 0%, #f4d03f 50%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Layout improvements */
.min-h-screen {
  position: relative;
  overflow-x: hidden;
}

/* Ensure proper spacing between sections */
section {
  position: relative;
  z-index: 1;
}

/* Grid improvements */
.grid {
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .grid {
    gap: 2rem;
  }
}

/* Prevent text overflow */
h1, h2, h3 {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Improve button spacing */
.mythology-button {
  min-width: 180px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Premium form styles */
.premium-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(51, 65, 85, 0.5);
  border: 1px solid rgb(71, 85, 105);
  border-radius: 0.75rem;
  color: white;
  transition: all 0.3s ease;
}

.premium-input::placeholder {
  color: rgb(148, 163, 184);
}

.premium-input:focus {
  outline: none;
  border-color: rgb(251, 191, 36);
  box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.5), 0 10px 15px -3px rgba(251, 191, 36, 0.1);
}

.premium-button {
  background: linear-gradient(90deg, rgb(251, 191, 36) 0%, rgb(245, 158, 11) 100%);
  color: rgb(15, 23, 42);
  font-weight: 700;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.premium-button:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(251, 191, 36, 0.25);
}

.premium-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Enhanced animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Premium gradient backgrounds */
.premium-gradient {
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
}

.premium-gradient-gold {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 5px;
  border: 2px solid #1e293b;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* Premium animations */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(251, 191, 36, 0.3); }
  50% { box-shadow: 0 0 40px rgba(251, 191, 36, 0.6); }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(251, 191, 36, 0.6);
    transform: scale(1.05);
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-sparkle {
  animation: sparkle 1.5s ease-in-out infinite;
}

/* Premium gradient backgrounds */
.bg-premium-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-luxury-gradient {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-mystical-gradient {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bg-divine-gradient {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Glass morphism effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Premium text effects */
.text-premium-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-luxury-gradient {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
}

/* Premium shadows */
.shadow-premium {
  box-shadow: 0 25px 50px -12px rgba(102, 126, 234, 0.25);
}

.shadow-luxury {
  box-shadow: 0 25px 50px -12px rgba(240, 147, 251, 0.25);
}

.shadow-mystical {
  box-shadow: 0 25px 50px -12px rgba(79, 172, 254, 0.25);
}

/* Interactive elements */
.interactive-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-hover:hover {
  transform: translateY(-2px) scale(1.02);
}

/* Premium borders */
.border-premium {
  border-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%) 1;
}

.border-luxury {
  border-image: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) 1;
}

import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Toaster } from 'react-hot-toast'

// Import components directly (no lazy loading for debugging)
import Navbar from './components/Navbar'
import Landing from './pages/Landing'
import Login from './pages/Login'
import Register from './pages/Register'
import Home from './pages/Home'
import Horoscope from './pages/Horoscope'
import DailyInstructions from './pages/DailyInstructions'
import FutureReadings from './pages/FutureReadings'
import Profile from './pages/Profile'

function App() {
  console.log('App component rendering...')

  return (
    <Router>
      <div className="min-h-screen bg-mythology-gradient text-white">
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'rgba(30, 41, 59, 0.95)',
              color: '#fff',
              border: '1px solid rgba(251, 191, 36, 0.3)',
              borderRadius: '12px',
              backdropFilter: 'blur(10px)',
            },
            success: {
              iconTheme: {
                primary: '#fbbf24',
                secondary: '#1e293b',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#1e293b',
              },
            },
          }}
        />

        <Routes>
            {/* Landing page without navbar */}
            <Route path="/" element={<Landing />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* App pages with navbar */}
            <Route path="/home" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <Home />
                </motion.main>
              </>
            } />
            <Route path="/horoscope" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <Horoscope />
                </motion.main>
              </>
            } />
            <Route path="/daily-instructions" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <DailyInstructions />
                </motion.main>
              </>
            } />
            <Route path="/future-readings" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <FutureReadings />
                </motion.main>
              </>
            } />
            <Route path="/profile" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <Profile />
                </motion.main>
              </>
            } />
        </Routes>
      </div>
    </Router>
  )
}

export default App

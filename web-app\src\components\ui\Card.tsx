import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '../../lib/utils'

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'mystical' | 'divine' | 'glass'
  glowEffect?: boolean
  hoverEffect?: boolean
}

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}
export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}
export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}
export interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {}
export interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', glowEffect = false, hoverEffect = true, children, ...props }, ref) => {
    // Base styles
    const baseStyles = "rounded-2xl border backdrop-blur-sm transition-all duration-300"

    // Variant styles
    const variantStyles = {
      default: "bg-slate-800/80 border-slate-700/50 shadow-xl",
      mystical: "bg-gradient-to-br from-slate-800/90 via-purple-900/20 to-slate-800/90 border-purple-500/30 shadow-2xl shadow-purple-500/10",
      divine: "bg-gradient-to-br from-slate-800/90 via-amber-900/20 to-slate-800/90 border-amber-500/30 shadow-2xl shadow-amber-500/10",
      glass: "bg-white/5 border-white/10 shadow-2xl backdrop-blur-xl"
    }

    return (
      <motion.div
        ref={ref}
        className={cn(
          baseStyles,
          variantStyles[variant],
          glowEffect && "animate-pulse",
          className
        )}
        whileHover={hoverEffect ? { y: -2, scale: 1.01 } : undefined}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        {...props}
      >
        {/* Glow Effect */}
        {glowEffect && (
          <div className="absolute inset-0 bg-gradient-to-r from-amber-400/10 via-yellow-500/10 to-amber-400/10 rounded-2xl blur-xl animate-pulse" />
        )}

        {/* Mystical Border Animation */}
        {variant === 'mystical' && (
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500/20 via-transparent to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        )}

        {/* Divine Border Animation */}
        {variant === 'divine' && (
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-amber-500/20 via-transparent to-amber-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        )}

        <div className="relative z-10">
          {children}
        </div>
      </motion.div>
    )
  }
)

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex flex-col space-y-1.5 p-6", className)}
      {...props}
    />
  )
)

const CardTitle = React.forwardRef<HTMLParagraphElement, CardTitleProps>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn("text-2xl font-bold leading-none tracking-tight text-white", className)}
      {...props}
    />
  )
)

const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn("text-sm text-slate-400", className)}
      {...props}
    />
  )
)

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
  )
)

const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex items-center p-6 pt-0", className)}
      {...props}
    />
  )
)

Card.displayName = "Card"
CardHeader.displayName = "CardHeader"
CardFooter.displayName = "CardFooter"
CardTitle.displayName = "CardTitle"
CardDescription.displayName = "CardDescription"
CardContent.displayName = "CardContent"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }
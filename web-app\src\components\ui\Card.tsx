import React from 'react'

import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '../../lib/utils'

const cardVariants = cva(
  "rounded-2xl border shadow-lg transition-all duration-300 relative overflow-hidden group",
  {
    variants: {
      variant: {
        default: "bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:border-slate-600/50 hover:shadow-xl",
        premium: "bg-gradient-to-br from-slate-800/60 to-slate-900/60 border-purple-500/30 backdrop-blur-md hover:border-purple-400/50 hover:shadow-2xl hover:shadow-purple-500/10",
        glass: "bg-white/10 border-white/20 backdrop-blur-xl hover:bg-white/15 hover:border-white/30 hover:shadow-2xl hover:shadow-white/5",
        luxury: "bg-gradient-to-br from-amber-50/5 to-yellow-50/5 border-amber-400/30 backdrop-blur-sm hover:border-amber-400/50 hover:shadow-2xl hover:shadow-amber-400/10",
        gradient: "bg-gradient-to-br from-purple-600/20 via-pink-600/20 to-blue-600/20 border-purple-400/30 backdrop-blur-md hover:from-purple-600/30 hover:via-pink-600/30 hover:to-blue-600/30",
        dark: "bg-slate-900/80 border-slate-800 backdrop-blur-sm hover:border-slate-700 hover:shadow-2xl",
        glow: "bg-slate-800/50 border-yellow-400/30 backdrop-blur-sm hover:border-yellow-400/50 hover:shadow-2xl hover:shadow-yellow-400/20",
      },
      size: {
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
        xl: "p-10",
      },
      hover: {
        none: "",
        lift: "hover:-translate-y-1",
        scale: "hover:scale-[1.02]",
        glow: "hover:shadow-2xl",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      hover: "lift",
    },
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean
  interactive?: boolean
  glowEffect?: boolean
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, hover, interactive, glowEffect, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(cardVariants({ variant, size, hover, className }))}
        {...props}
      >
        {/* Glow effect */}
        {glowEffect && (
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 via-transparent to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl" />
        )}
        
        {/* Shimmer effect for premium variants */}
        {(variant === 'premium' || variant === 'luxury') && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
        )}
        
        {/* Content */}
        <div className="relative z-10">
          {children}
        </div>
      </div>
    )
  }
)

Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 pb-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-bold leading-none tracking-tight bg-gradient-to-r from-yellow-400 to-white bg-clip-text text-transparent",
      className
    )}
    {...props}
  >
    {children}
  </h3>
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-slate-400 leading-relaxed", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center pt-6", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }

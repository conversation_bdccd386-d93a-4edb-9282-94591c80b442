import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import toast from 'react-hot-toast'
import {
  Crown,
  Mail,
  Lock,
  UserPlus,
  ArrowLeft,
  Sparkles,
  Star,
  User,
  Calendar,
  Eye,
  EyeOff,
  Shield,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/Card'

const registerSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  birthDate: z.string().min(1, 'Birth date is required for accurate readings'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type RegisterFormData = z.infer<typeof registerSchema>

const Register: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState(0)

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  })

  const password = watch('password', '')

  // Calculate password strength
  useEffect(() => {
    let strength = 0
    if (password.length >= 8) strength += 25
    if (/[A-Z]/.test(password)) strength += 25
    if (/[a-z]/.test(password)) strength += 25
    if (/\d/.test(password)) strength += 25
    setPasswordStrength(strength)
  }, [password])

  const onSubmit = async (_data: RegisterFormData) => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      toast.success('Account created successfully! Welcome to Kubera!')

      // Redirect to login page after successful registration
      setTimeout(() => {
        window.location.href = '/login'
      }, 1500)

    } catch (error) {
      toast.error('Registration failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignUp = async () => {
    setIsGoogleLoading(true)

    try {
      // Simulate Google OAuth
      await new Promise(resolve => setTimeout(resolve, 1500))

      toast.success('Successfully signed up with Google!')

      setTimeout(() => {
        window.location.href = '/home'
      }, 1000)

    } catch (error) {
      toast.error('Google sign-up failed. Please try again.')
    } finally {
      setIsGoogleLoading(false)
    }
  }

  const getPasswordStrengthColor = () => {
    if (passwordStrength < 25) return 'bg-red-500'
    if (passwordStrength < 50) return 'bg-orange-500'
    if (passwordStrength < 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getPasswordStrengthText = () => {
    if (passwordStrength < 25) return 'Weak'
    if (passwordStrength < 50) return 'Fair'
    if (passwordStrength < 75) return 'Good'
    return 'Strong'
  }

  return (
    <div className="min-h-screen relative overflow-hidden">

      {/* Enhanced Background with Dynamic Gradients */}
      <div className="absolute inset-0">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/95 via-purple-900/85 to-slate-900/95" />
        <div className="absolute inset-0 bg-gradient-to-tr from-amber-900/20 via-transparent to-purple-900/30" />
        <div className="absolute inset-0 bg-black/40" />

        {/* Animated Gradient Overlay */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-amber-600/10 to-purple-600/10"
          animate={{
            background: [
              'linear-gradient(45deg, rgba(147, 51, 234, 0.1) 0%, rgba(245, 158, 11, 0.1) 50%, rgba(147, 51, 234, 0.1) 100%)',
              'linear-gradient(45deg, rgba(245, 158, 11, 0.1) 0%, rgba(147, 51, 234, 0.1) 50%, rgba(245, 158, 11, 0.1) 100%)',
              'linear-gradient(45deg, rgba(147, 51, 234, 0.1) 0%, rgba(245, 158, 11, 0.1) 50%, rgba(147, 51, 234, 0.1) 100%)'
            ]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>

      {/* Enhanced Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(40)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: Math.random() * 6 + 2,
              height: Math.random() * 6 + 2,
              background: i % 3 === 0
                ? 'radial-gradient(circle, rgba(251, 191, 36, 0.4) 0%, rgba(245, 158, 11, 0.2) 70%, transparent 100%)'
                : i % 3 === 1
                ? 'radial-gradient(circle, rgba(147, 51, 234, 0.4) 0%, rgba(126, 34, 206, 0.2) 70%, transparent 100%)'
                : 'radial-gradient(circle, rgba(236, 72, 153, 0.4) 0%, rgba(219, 39, 119, 0.2) 70%, transparent 100%)'
            }}
            animate={{
              y: [0, -150 - Math.random() * 100, 0],
              x: [0, Math.random() * 50 - 25, 0],
              opacity: [0, 0.8, 0],
              scale: [0, 1.2, 0],
              rotate: [0, 360],
            }}
            transition={{
              duration: Math.random() * 4 + 6,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Floating Mythological Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`myth-${i}`}
            className="absolute opacity-10"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              rotate: [0, 360],
              opacity: [0.05, 0.15, 0.05],
            }}
            transition={{
              duration: 15 + Math.random() * 10,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeInOut"
            }}
          >
            {i % 4 === 0 && <Crown size={24 + Math.random() * 16} className="text-yellow-400" />}
            {i % 4 === 1 && <Sparkles size={20 + Math.random() * 12} className="text-purple-400" />}
            {i % 4 === 2 && <Star size={18 + Math.random() * 14} className="text-amber-400" />}
            {i % 4 === 3 && <Shield size={22 + Math.random() * 10} className="text-blue-400" />}
          </motion.div>
        ))}
      </div>

      {/* Navigation */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative z-20 p-8"
      >
        <Button variant="ghost" asChild>
          <Link to="/" className="flex items-center gap-2">
            <ArrowLeft size={20} />
            <span>Back to Home</span>
          </Link>
        </Button>
      </motion.div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 40, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
          className="w-full max-w-xl"
        >
          <Card variant="premium" className="overflow-hidden backdrop-blur-xl" glowEffect>
            <CardHeader className="text-center pb-8 relative">
              {/* Enhanced Crown Animation */}
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 1.2, delay: 0.2, type: "spring", stiffness: 80 }}
                className="mb-8 relative"
              >
                <motion.div
                  className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 rounded-full shadow-2xl relative"
                  animate={{
                    boxShadow: [
                      '0 25px 50px -12px rgba(251, 191, 36, 0.25)',
                      '0 25px 50px -12px rgba(251, 191, 36, 0.5)',
                      '0 25px 50px -12px rgba(251, 191, 36, 0.25)'
                    ]
                  }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                >
                  <motion.div
                    animate={{ rotate: [0, 5, -5, 0] }}
                    transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <Crown size={44} className="text-slate-900" />
                  </motion.div>

                  {/* Rotating Ring */}
                  <motion.div
                    className="absolute inset-0 rounded-full border-2 border-yellow-300/30"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  />

                  {/* Shimmer Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shimmer rounded-full" />
                </motion.div>

                {/* Floating Sparkles around Crown */}
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute"
                    style={{
                      left: `${50 + 40 * Math.cos((i * 60) * Math.PI / 180)}%`,
                      top: `${50 + 40 * Math.sin((i * 60) * Math.PI / 180)}%`,
                    }}
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: i * 0.3,
                      ease: "easeInOut"
                    }}
                  >
                    <Sparkles size={12} className="text-yellow-400" />
                  </motion.div>
                ))}
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
              >
                <CardTitle className="text-4xl mb-3 bg-gradient-to-r from-yellow-400 via-amber-300 to-yellow-500 bg-clip-text text-transparent">
                  Begin Your Journey
                </CardTitle>
                <CardDescription className="text-lg text-slate-300">
                  Create your account to unlock divine wisdom
                </CardDescription>
              </motion.div>
            </CardHeader>

            <CardContent className="px-8 pb-8">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
                {/* Form Fields with Staggered Animation */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.7 }}
                  className="space-y-6"
                >
                  {/* Full Name Field */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                  >
                    <Input
                      label="Full Name"
                      type="text"
                      placeholder="Enter your full name"
                      variant="premium"
                      size="lg"
                      leftIcon={<User size={20} />}
                      error={errors.fullName?.message}
                      {...register('fullName')}
                    />
                  </motion.div>

                  {/* Email Field */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.9 }}
                  >
                    <Input
                      label="Email Address"
                      type="email"
                      placeholder="Enter your email"
                      variant="premium"
                      size="lg"
                      leftIcon={<Mail size={20} />}
                      error={errors.email?.message}
                      {...register('email')}
                    />
                  </motion.div>

                  {/* Birth Date Field */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 1.0 }}
                  >
                    <Input
                      label="Birth Date"
                      type="date"
                      variant="premium"
                      size="lg"
                      leftIcon={<Calendar size={20} />}
                      error={errors.birthDate?.message}
                      {...register('birthDate')}
                    />
                  </motion.div>

                  {/* Password Field with Strength Indicator */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 1.1 }}
                    className="space-y-3"
                  >
                    <div className="relative">
                      <Input
                        label="Password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Create a strong password"
                        variant="premium"
                        size="lg"
                        leftIcon={<Lock size={20} />}
                        rightIcon={
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="text-slate-400 hover:text-yellow-400 transition-colors duration-200"
                          >
                            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                          </button>
                        }
                        error={errors.password?.message}
                        {...register('password')}
                      />
                    </div>

                    {/* Password Strength Indicator */}
                    <AnimatePresence>
                      {password && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="space-y-2"
                        >
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-slate-400">Password Strength</span>
                            <span className={`font-medium ${
                              passwordStrength < 25 ? 'text-red-400' :
                              passwordStrength < 50 ? 'text-orange-400' :
                              passwordStrength < 75 ? 'text-yellow-400' :
                              'text-green-400'
                            }`}>
                              {getPasswordStrengthText()}
                            </span>
                          </div>
                          <div className="w-full bg-slate-700 rounded-full h-2 overflow-hidden">
                            <motion.div
                              className={`h-full rounded-full ${getPasswordStrengthColor()}`}
                              initial={{ width: 0 }}
                              animate={{ width: `${passwordStrength}%` }}
                              transition={{ duration: 0.5, ease: "easeOut" }}
                            />
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>

                  {/* Confirm Password Field */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 1.2 }}
                  >
                    <Input
                      label="Confirm Password"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Confirm your password"
                      variant="premium"
                      size="lg"
                      leftIcon={<Lock size={20} />}
                      rightIcon={
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="text-slate-400 hover:text-yellow-400 transition-colors duration-200"
                        >
                          {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                        </button>
                      }
                      error={errors.confirmPassword?.message}
                      {...register('confirmPassword')}
                    />
                  </motion.div>
                </motion.div>

                {/* Submit Button */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.3 }}
                  className="pt-4"
                >
                  <Button
                    type="submit"
                    variant="premium"
                    size="xl"
                    loading={isLoading}
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-500 hover:via-pink-500 hover:to-blue-500 shadow-2xl hover:shadow-purple-500/30 hover:scale-[1.02] transition-all duration-300"
                    leftIcon={!isLoading && <UserPlus size={22} />}
                  >
                    {isLoading ? 'Creating Account...' : 'Create Account'}
                  </Button>
                </motion.div>
              </form>

              {/* Divider */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 1.4 }}
                className="relative my-8"
              >
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-slate-700/50"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-4 bg-slate-900/80 text-slate-400 backdrop-blur-sm">or continue with</span>
                </div>
              </motion.div>

              {/* Google Sign Up */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.5 }}
              >
                <Button
                  type="button"
                  variant="glass"
                  size="xl"
                  loading={isGoogleLoading}
                  disabled={isGoogleLoading}
                  onClick={handleGoogleSignUp}
                  className="w-full hover:bg-white/20 hover:scale-[1.02] transition-all duration-300"
                  leftIcon={!isGoogleLoading && (
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                      <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                      <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                      <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                    </svg>
                  )}
                >
                  {isGoogleLoading ? 'Signing up with Google...' : 'Sign up with Google'}
                </Button>
              </motion.div>

              {/* Sign In Link */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 1.6 }}
                className="text-center mt-8 pt-6 border-t border-slate-700/50"
              >
                <p className="text-slate-400">
                  Already have an account?{' '}
                  <Link
                    to="/login"
                    className="text-transparent bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text font-semibold hover:from-purple-300 hover:to-pink-300 transition-all duration-300 hover:underline"
                  >
                    Sign in here
                  </Link>
                </p>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Enhanced Decorative Elements */}
      <div className="absolute top-20 right-20 opacity-30 pointer-events-none">
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.2, 1],
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 4, repeat: Infinity, ease: "easeInOut" }
          }}
        >
          <Star size={28} className="text-yellow-400 drop-shadow-lg" />
        </motion.div>
      </div>

      <div className="absolute bottom-20 left-20 opacity-30 pointer-events-none">
        <motion.div
          animate={{
            rotate: -360,
            y: [0, -10, 0],
          }}
          transition={{
            rotate: { duration: 25, repeat: Infinity, ease: "linear" },
            y: { duration: 3, repeat: Infinity, ease: "easeInOut" }
          }}
        >
          <Sparkles size={24} className="text-purple-400 drop-shadow-lg" />
        </motion.div>
      </div>

      <div className="absolute top-1/3 left-10 opacity-20 pointer-events-none">
        <motion.div
          animate={{
            rotate: [0, 15, -15, 0],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Crown size={20} className="text-amber-400" />
        </motion.div>
      </div>

      <div className="absolute bottom-1/3 right-10 opacity-20 pointer-events-none">
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Shield size={18} className="text-blue-400" />
        </motion.div>
      </div>
    </div>
  )
}

export default Register

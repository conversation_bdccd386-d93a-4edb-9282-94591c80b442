import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '../../lib/utils'

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'destructive' | 'mystical' | 'divine'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  glowEffect?: boolean
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    loading = false,
    leftIcon,
    rightIcon,
    glowEffect = false,
    children,
    disabled,
    ...props
  }, ref) => {
    const isDisabled = disabled || loading

    // Base styles
    const baseStyles = "relative inline-flex items-center justify-center gap-2 font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden group"

    // Variant styles
    const variantStyles = {
      primary: "bg-gradient-to-r from-amber-500 to-yellow-600 text-slate-900 hover:from-amber-400 hover:to-yellow-500 focus:ring-amber-500 shadow-lg hover:shadow-amber-500/25",
      secondary: "bg-gradient-to-r from-slate-700 to-slate-600 text-white hover:from-slate-600 hover:to-slate-500 focus:ring-slate-500 shadow-lg hover:shadow-slate-500/25",
      ghost: "text-amber-400 hover:text-amber-300 hover:bg-amber-500/10 focus:ring-amber-500",
      destructive: "bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-500 hover:to-red-600 focus:ring-red-500 shadow-lg hover:shadow-red-500/25",
      mystical: "bg-gradient-to-r from-purple-600 via-indigo-600 to-purple-700 text-white hover:from-purple-500 hover:via-indigo-500 hover:to-purple-600 focus:ring-purple-500 shadow-lg hover:shadow-purple-500/25",
      divine: "bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600 text-slate-900 hover:from-amber-300 hover:via-yellow-400 hover:to-amber-500 focus:ring-amber-500 shadow-xl hover:shadow-amber-500/30"
    }

    // Size styles
    const sizeStyles = {
      sm: "h-9 px-3 py-2 text-sm rounded-lg",
      md: "h-11 px-4 py-2.5 text-sm rounded-xl",
      lg: "h-12 px-6 py-3 text-base rounded-xl",
      xl: "h-14 px-8 py-4 text-lg rounded-2xl"
    }

    return (
      <motion.button
        ref={ref}
        className={cn(
          baseStyles,
          variantStyles[variant],
          sizeStyles[size],
          glowEffect && "animate-pulse",
          className
        )}
        disabled={isDisabled}
        whileHover={{ scale: isDisabled ? 1 : 1.02 }}
        whileTap={{ scale: isDisabled ? 1 : 0.98 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
        {...props}
      >
        {/* Mystical shimmer effect */}
        {(variant === 'mystical' || variant === 'divine') && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
        )}

        {/* Glow effect */}
        {glowEffect && (
          <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 via-yellow-500/20 to-amber-400/20 rounded-full blur-lg animate-pulse" />
        )}

        {/* Loading spinner */}
        {loading && (
          <motion.div
            className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        )}

        {/* Left icon */}
        {leftIcon && !loading && (
          <span className="flex-shrink-0">
            {leftIcon}
          </span>
        )}

        {/* Button text */}
        <span className="relative z-10">
          {children}
        </span>

        {/* Right icon */}
        {rightIcon && !loading && (
          <span className="flex-shrink-0">
            {rightIcon}
          </span>
        )}
      </motion.button>
    )
  }
)

Button.displayName = "Button"

export { Button }
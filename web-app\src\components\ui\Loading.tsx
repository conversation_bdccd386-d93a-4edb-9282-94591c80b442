import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '../../lib/utils'
import { Crown, Sparkles, Star } from 'lucide-react'

export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'spinner' | 'dots' | 'crown' | 'mystical'
  text?: string
  className?: string
}

const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  variant = 'spinner',
  text,
  className
}) => {
  // Size styles
  const sizeStyles = {
    sm: { container: 'w-4 h-4', text: 'text-sm' },
    md: { container: 'w-8 h-8', text: 'text-base' },
    lg: { container: 'w-12 h-12', text: 'text-lg' },
    xl: { container: 'w-16 h-16', text: 'text-xl' }
  }

  const iconSizes = {
    sm: 16,
    md: 24,
    lg: 32,
    xl: 40
  }

  const renderSpinner = () => (
    <motion.div
      className={cn(
        'border-2 border-amber-500/30 border-t-amber-500 rounded-full',
        sizeStyles[size].container
      )}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    />
  )

  const renderDots = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="w-2 h-2 bg-amber-500 rounded-full"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.2
          }}
        />
      ))}
    </div>
  )

  const renderCrown = () => (
    <motion.div
      className="text-amber-500"
      animate={{
        rotate: [0, 360],
        scale: [1, 1.1, 1]
      }}
      transition={{
        rotate: { duration: 2, repeat: Infinity, ease: 'linear' },
        scale: { duration: 1, repeat: Infinity, ease: 'easeInOut' }
      }}
    >
      <Crown size={iconSizes[size]} />
    </motion.div>
  )

  const renderMystical = () => (
    <div className="relative">
      {/* Central crown */}
      <motion.div
        className="text-amber-500 absolute inset-0 flex items-center justify-center"
        animate={{ rotate: 360 }}
        transition={{ duration: 3, repeat: Infinity, ease: 'linear' }}
      >
        <Crown size={iconSizes[size]} />
      </motion.div>

      {/* Orbiting sparkles */}
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="absolute text-purple-400"
          style={{
            left: '50%',
            top: '50%',
            transformOrigin: `${20 + size * 4}px 0px`
          }}
          animate={{ rotate: 360 }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'linear',
            delay: i * 0.6
          }}
        >
          <Sparkles size={iconSizes[size] * 0.4} />
        </motion.div>
      ))}

      {/* Outer ring */}
      <motion.div
        className={cn(
          'border border-amber-500/30 rounded-full',
          sizeStyles[size].container
        )}
        animate={{ rotate: -360 }}
        transition={{ duration: 4, repeat: Infinity, ease: 'linear' }}
      />
    </div>
  )

  const renderVariant = () => {
    switch (variant) {
      case 'dots':
        return renderDots()
      case 'crown':
        return renderCrown()
      case 'mystical':
        return renderMystical()
      default:
        return renderSpinner()
    }
  }

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-3', className)}>
      <div className="relative flex items-center justify-center">
        {renderVariant()}
      </div>

      {text && (
        <motion.p
          className={cn('text-slate-300 font-medium', sizeStyles[size].text)}
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}
        >
          {text}
        </motion.p>
      )}
    </div>
  )
}

Loading.displayName = "Loading"

export { Loading }
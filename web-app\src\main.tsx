
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App'

console.log('main.tsx is loading...')

const rootElement = document.getElementById('root')
console.log('Root element:', rootElement)

if (rootElement) {
  const root = createRoot(rootElement)
  console.log('React root created, rendering App...')

  root.render(<App />)

  console.log('App rendered successfully!')
} else {
  console.error('Root element not found!')
}
